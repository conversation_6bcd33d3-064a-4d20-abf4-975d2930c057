import React, { useEffect, useState } from "react";
import { View, Text, FlatList, StyleSheet, ScrollView } from "react-native";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import ContactCard from "../../components/ContactCard";
import SortOptionsBox from "../../components/SortOptionsBox";
import icons from "../../assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { showToast } from "../../utils/toastConfig";
import {
  setTagsData,
  setProfileContactsData,
  getTags,
} from "../../redux/features/mainSlice";
import MyText from "../../components/MyText";
import {
  getContacts,
  getProfileContacts,
} from "../../redux/features/contactSlice";
import { PrimaryButton } from "../../components/Button";
import AppLoader from "../../components/AppLoader";
import { addContactsToProfile } from "../../redux/features/SharingProfileSlice";
import ChipSelector from "../../components/ChipSelector";
import ContactList from "../../components/ContactList";
import { groupContactsByAlphabet } from "../../utils/commonHelpers";

const formatContacts = (data = []) =>
  data.map((contact) => {
    // console.log("🚀 ~ data.map ~ contact:", contact);
    return {
      id: contact._id,
      name: `${contact.firstName || ""} ${contact.middleName || ""} ${
        contact.lastName || ""
      }`.trim(),
      // Keep individual name parts for ContactList component
      firstName: contact.firstName || "",
      middleName: contact.middleName || "",
      lastName: contact.lastName || "",
      phone: contact.phoneNumbers?.[0]?.number || "",
      is_favorite: contact.is_favorite,
      imgUrl: contact.profile_image || "",
      // Keep original contact data for search purposes
      originalContact: contact,
    };
  });

const getContactsList = (route, contacts, profileContactsData) => {
  if (route?.params?.fromProfile && profileContactsData?.data?.result) {
    return formatContacts(profileContactsData.data.result);
  }
  if (contacts?.data?.result) {
    return formatContacts(contacts?.data?.result);
  }
  return [];
};

const getInitialSelectedContacts = (
  route,
  tagsData,
  selectedProfileContactsData
) => {
  if (tagsData && !route?.params?.fromProfile) {
    return tagsData.contacts;
  }
  if (route?.params?.fromProfile && selectedProfileContactsData) {
    return (selectedProfileContactsData || []).filter(
      (contact) => contact && Object.keys(contact).length > 0
    );
  }
  if (route?.params?.selectedContacts) {
    return route.params.selectedContacts;
  }
  return [];
};

const AddContactsScreen = ({ navigation, route }) => {
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [activeSort, setActiveSort] = useState("az");
  const [activeFilter, setActiveFilter] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [isSortBoxVisible, setSortBoxVisible] = useState(false);
  const [isFilterBoxVisible, setFilterBoxVisible] = useState(false);

  const contactsDataState = useSelector(
    (state) => state.contactSlice.getContacts
  );
  const { data: contacts, loading: contactsLoading } = contactsDataState;

  const profileContacts = useSelector(
    (state) => state.contactSlice.getProfileContacts
  );
  const { data: profileContactsData, loading } = profileContacts;
  const selectedProfileContactsData = useSelector(
    (state) => state.mainSlice.profileContactsData
  );
  const tagsData = useSelector((state) => state.mainSlice.tagsData);
  const dispatch = useDispatch();
  const [contactsList, setContactsList] = useState([]);

  // Create tag options from tagsData
  const tagOptions = React.useMemo(() => {
    if (tagsData && Array.isArray(tagsData.tags)) {
      return tagsData.tags.map((tag) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        contacts: tag.contacts || tag.members || [],
      }));
    } else if (tagsData && Array.isArray(tagsData.result)) {
      return tagsData.result.map((tag) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        contacts: tag.contacts || tag.members || [],
      }));
    }
    return [];
  }, [tagsData]);

  // Get contacts for selected tag
  const tagContacts = React.useMemo(() => {
    if (selectedTag) {
      const found = tagOptions.find((opt) => opt.value === selectedTag);
      return found ? found.contacts : [];
    }
    return [];
  }, [selectedTag, tagOptions]);

  // Fetch contacts and tags based on navigation source
  useEffect(() => {
    if (route?.params?.fromProfile) {
      dispatch(getProfileContacts());
    } else {
      dispatch(getContacts());
    }
    // Fetch tags for filtering
    dispatch(getTags());
  }, [route?.params, dispatch]);

  // Update contactsList when contacts or profileContacts change
  useEffect(() => {
    const tempContacts = getContactsList(route, contacts, profileContactsData);
    console.log("🚀 ~ useEffect ~ tempContacts:", tempContacts);
    setContactsList(tempContacts);
  }, [contacts, profileContactsData, route?.params]);

  // Sync selectedContacts with Redux or route params
  useEffect(() => {
    setSelectedContacts(
      getInitialSelectedContacts(route, tagsData, selectedProfileContactsData)
    );
  }, [tagsData, selectedProfileContactsData, route?.params]);

  // Toggle contact selection
  const toggleSelect = (item) => {
    console.log("🚀 ~ toggleSelect ~ item:", item);
    if (!item?.id) return;
    setSelectedContacts((prev) => {
      console.log("🚀 ~ toggleSelect ~ prev:", prev);
      return prev.some((contact) => contact.id === item.id)
        ? prev.filter((contact) => contact.id !== item.id)
        : [...prev, item];
    });
  };
  console.log(selectedContacts, "selectedContacts");

  const handleAddContactToProfile = async () => {
    if (!selectedContacts.length) {
      showToast("error", "Error", "Please select at least one contact.");
      return;
    }
    const addApiBody = {
      addInProfile: selectedContacts.map((contact) => ({
        memberId: contact.id,
      })),
    };
    try {
      const response = await dispatch(
        addContactsToProfile(route?.params?.fromProfile, addApiBody)
      );

      const isSuccess =
        response.success || (response.payload && response.payload.success);
      showToast(
        isSuccess ? "success" : "error",
        response?.payload?.message ||
          response?.message ||
          (isSuccess
            ? "Contacts added successfully."
            : "Failed to add contacts.")
      );
      navigation.goBack();
    } catch (error) {
      showToast(
        "error",
        error?.message || "Something went wrong while adding contacts."
      );
    }
  };

  // Handle add to tag/profile
  const handleAddToTag = () => {
    if (!selectedContacts.length) {
      showToast(
        "error",
        "Please select at least one contact to add to the tag."
      );
      return;
    }
    try {
      if (typeof route?.params?.fromProfile === "string") {
        // handleAddContactToProfile();
        // navigation.navigate("EditSharingProfileScreen", {
        //   profileId: route?.params?.fromProfile,
        //   selectedContacts: { selectedContacts },
        //   merge: true,
        // });
        dispatch(setProfileContactsData(selectedContacts));
        navigation.goBack();
      } else if (route?.params?.fromProfile) {
        dispatch(setProfileContactsData(selectedContacts));
        navigation.goBack();
      } else {
        dispatch(setTagsData({ ...tagsData, contacts: selectedContacts }));
        setSelectedContacts([]);
        navigation.goBack();
      }
    } catch (err) {
      console.error("Error setting tags data:", err);
    }
  };

  // Sort and Filter Handlers
  const handleSortIconPress = () => {
    setSortBoxVisible((prev) => !prev);
    setFilterBoxVisible(false);
  };
  const handleFilterIconPress = () => {
    setFilterBoxVisible((prev) => !prev);
    setSortBoxVisible(false);
  };
  const handleSortOption = (option) => {
    setSortBoxVisible(false);
    const sortValue = option ? option.value : null;
    setActiveSort(sortValue);

    // Clear any active filter when sorting is applied
    if (sortValue && activeFilter) {
      setActiveFilter(null);
      // Reset to default API call when clearing filter
      if (route?.params?.fromProfile) {
        dispatch(getProfileContacts());
      } else {
        dispatch(getContacts());
      }
    }
  };
  const handleFilterOption = (option) => {
    setFilterBoxVisible(false);
    const filterValue = option ? option.value : null;
    setActiveFilter(filterValue);

    // Handle API-based filters
    if (filterValue === "recent") {
      // Fetch recently added contacts from API
      if (route?.params?.fromProfile) {
        dispatch(getProfileContacts({ sort: -1 }));
      } else {
        dispatch(getContacts({ sort: -1 }));
      }
    } else if (filterValue === "tag") {
      // Handle tags filter - reset selected tag when toggling
      if (activeFilter === "tag") {
        setActiveFilter(null);
        setSelectedTag(null);
      } else {
        setActiveFilter("tag");
      }
    } else if (filterValue === null) {
      // Reset to default when filter is cleared
      setSelectedTag(null);
      if (route?.params?.fromProfile) {
        dispatch(getProfileContacts());
      } else {
        dispatch(getContacts());
      }
    }
  };

  // Filter contacts by search query and filter/sort
  let filteredContacts = React.useMemo(() => {
    let baseContacts;

    // Apply tag filter first if active
    if (activeFilter === "tag" && selectedTag) {
      // Convert tag contacts to the same format as contactsList
      baseContacts = tagContacts.map((contact) => ({
        id: contact._id,
        name: `${contact.firstName || ""} ${contact.middleName || ""} ${
          contact.lastName || ""
        }`.trim(),
        // Keep individual name parts for ContactList component
        firstName: contact.firstName || "",
        middleName: contact.middleName || "",
        lastName: contact.lastName || "",
        phone: contact.phoneNumbers?.[0]?.number || "",
        is_favorite: contact.is_favorite,
        imgUrl: contact.profile_image || "",
        originalContact: contact,
      }));
    } else {
      baseContacts = contactsList;
    }

    // Apply search filter
    return baseContacts.filter((contact) => {
      const search = searchQuery.trim().toLowerCase();
      if (!search) return true;

      const name = contact.name.toLowerCase();
      const phone = contact.phone.toLowerCase();

      // Add city search functionality
      const cities = [];
      const originalContact = contact.originalContact;

      if (originalContact) {
        // Check home address city
        if (originalContact.addresses_home?.city) {
          cities.push(originalContact.addresses_home.city.toLowerCase());
        }

        // Check other address city
        if (originalContact.addresses_other?.city) {
          cities.push(originalContact.addresses_other.city.toLowerCase());
        }

        // Check additional addresses array
        if (Array.isArray(originalContact.addresses)) {
          originalContact.addresses.forEach((address) => {
            if (address?.city) {
              cities.push(address.city.toLowerCase());
            }
          });
        }
      }

      const cityString = cities.join(" ");

      return (
        name.includes(search) ||
        phone.includes(search) ||
        cityString.includes(search)
      );
    });
  }, [contactsList, activeFilter, selectedTag, tagContacts, searchQuery]);
  // Apply additional filters and sorting
  const finalFilteredContacts = React.useMemo(() => {
    let result = [...filteredContacts];

    // Apply Favourites filter
    if (activeFilter === "favourites") {
      result = result.filter((c) => c.is_favorite === true);
    }
    // Note: Recently Added filter is handled via API call in handleFilterOption
    // No local filtering needed for "recent" as it's sorted by the backend

    // Apply sort only when no filter is active or when tag filter is active
    if (!activeFilter || activeFilter === "tag") {
      if (activeSort === "az") {
        result = result.sort((a, b) => a.name.localeCompare(b.name));
      } else if (activeSort === "za") {
        result = result.sort((a, b) => b.name.localeCompare(a.name));
      }
    }

    return result;
  }, [filteredContacts, activeFilter, activeSort]);

  // Toggle select all contacts
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedContacts([]);
      setIsAllSelected(false);
    } else {
      setSelectedContacts(finalFilteredContacts);
      setIsAllSelected(true);
    }
  };

  // Update isAllSelected when selectedContacts or finalFilteredContacts change
  useEffect(() => {
    setIsAllSelected(
      finalFilteredContacts.length > 0 &&
        finalFilteredContacts.every((contact) =>
          selectedContacts.some((sel) => sel.id === contact.id)
        )
    );
  }, [selectedContacts, finalFilteredContacts]);

  // console.log("🚀 ~ AddContactsScreen ~ filteredContacts:", filteredContacts);

  return (
    <View style={styles.container}>
      <Header
        title="Add Contacts"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <AppLoader isLoading={loading || contactsLoading} />
      <View style={styles.searchContainer}>
        <SearchBar
          placeholder="Search here"
          value={searchQuery}
          onChangeText={setSearchQuery}
          rightIcon={icons.filterIcon}
          onPressRightIcon1={handleSortIconPress}
          onPressRightIcon2={handleFilterIconPress}
        />
        {/* Sort and Filter Dropdowns */}
        {isSortBoxVisible && (
          <SortOptionsBox
            options={[
              { label: "Alphabetically(A-Z)", value: "az" },
              { label: "Alphabetically(Z-A)", value: "za" },
            ]}
            onSelect={handleSortOption}
            style={[styles.sortBoxOverlay, { right: 60 }]}
            optionStyle={styles.sortBoxOption}
            optionTextStyle={styles.optionText}
            activeValue={activeSort}
            allowDeselect={true}
          />
        )}
        {isFilterBoxVisible && (
          <SortOptionsBox
            options={[
              { label: "Favourites", value: "favourites" },
              { label: "Recently Added", value: "recent" },
              { label: "Tags", value: "tag" },
            ]}
            onSelect={handleFilterOption}
            style={[styles.sortBoxOverlay, { right: 20 }]}
            optionStyle={styles.sortBoxOption}
            optionTextStyle={styles.optionText}
            activeValue={activeFilter}
            allowDeselect={true}
          />
        )}
      </View>

      {/* Tag Chips */}
      {activeFilter === "tag" && (
        <View style={styles.chipsContainer}>
          {tagOptions.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <MyText p children={"No Tags Created"} />
            </View>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <ChipSelector
                options={tagOptions}
                selectedValue={selectedTag}
                onSelect={(value) =>
                  setSelectedTag(value === selectedTag ? null : value)
                }
                containerStyle={styles.chipSelectorContainer}
              />
            </ScrollView>
          )}
        </View>
      )}

      <View style={styles.contactHeader}>
        <MyText style={styles.contactLabel}>Contacts</MyText>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <MyText
            onPress={handleSelectAll}
            p
            regular
            underline
            style={{ marginRight: 10 }}
          >
            {isAllSelected ? "Deselect All" : "Select All"}
          </MyText>
        </View>
      </View>
      {/* Conditional rendering based on sort type */}
      {(activeSort === "az" || activeSort === "za") && !activeFilter ? (
        <ContactList
          contacts={groupContactsByAlphabet(finalFilteredContacts, activeSort)}
          showAlphabetList={true}
          mode="select"
          selectedContacts={selectedContacts}
          onSelectionChange={(contact) => toggleSelect(contact)}
          reverseAlphabet={activeSort === "za"}
        />
      ) : (
        <FlatList
          data={finalFilteredContacts}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <ContactCard
              name={item.name}
              phone={item.phone}
              isSelected={selectedContacts.some(
                (contact) => contact.id === item.id
              )}
              mode="select"
              onPress={() => toggleSelect(item)}
              imgUrl={item.imgUrl}
            />
          )}
          contentContainerStyle={styles.list}
          ListEmptyComponent={
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                height: 500,
              }}
            >
              <Text>No Contacts Found</Text>
            </View>
          }
        />
      )}
      <PrimaryButton
        title={route?.params?.fromProfile ? "Add to profile" : "Add to Tag"}
        onPress={handleAddToTag}
        style={{ bottom: 30 }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  contactHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: "#f2f2f2",
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  selectLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#555",
  },
  list: {
    paddingBottom: 80,
  },
  addButton: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  selectAllLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2E64FE",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 50,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 120,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  optionText: {
    alignSelf: "left",
  },
  chipsContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  emptyStateContainer: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  chipSelectorContainer: {
    width: "100%",
    gap: 10,
  },
});

export default AddContactsScreen;
